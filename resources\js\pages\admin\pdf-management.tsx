import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    FileText,
    Upload,
    Download,
    Trash2,
    Settings,
    CheckCircle,
    XCircle,
    AlertTriangle,
    HardDrive,
    Plus,
    Edit,
    Eye,
    Lock,
    Unlock,
} from 'lucide-react';
import { useState, useRef } from 'react';
import { router, useForm } from '@inertiajs/react';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import DataTable from '@/components/admin/DataTable';
import StatusBadge from '@/components/admin/StatusBadge';
import StatsCards from '@/components/admin/StatsCards';

interface Template {
    id: number;
    template_name: string;
    department_name: string;
    program_type: string;
    is_active: boolean;
    created_at: string;
    creator_name: string;
    questions_count: number;
}

interface PdfManagementProps {
    templates: Template[];
    breadcrumbs: { title: string; href: string }[];
    pdf_settings: {
        max_upload_size: number;
        allowed_types: string[];
        storage_path: string;
    };
    submission_settings: {
        allow_pdf_upload: boolean;
        allow_questionnaire_submission: boolean;
    };
}

export default function PdfManagement({ templates, breadcrumbs, pdf_settings, submission_settings }: PdfManagementProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');
    const [activeTab, setActiveTab] = useState('templates');
    const fileInputRef = useRef<HTMLInputElement>(null);

    // 問卷模板相關狀態
    const [showCreateQuestionnaireForm, setShowCreateQuestionnaireForm] = useState(false);
    const [editingQuestionnaire, setEditingQuestionnaire] = useState<Template | null>(null);

    // 提交方式設定狀態
    const [allowPdfUpload, setAllowPdfUpload] = useState(submission_settings.allow_pdf_upload);
    const [allowQuestionnaireSubmission, setAllowQuestionnaireSubmission] = useState(submission_settings.allow_questionnaire_submission);

    // 上傳表單
    const { data, setData, post, processing, errors, reset } = useForm({
        template_file: null as File | null,
        template_name: '',
        department_name: '',
        program_type: '',
        description: '',
    });

    // 過濾模板
    const filteredTemplates = templates.filter((template) => {
        const matchesSearch =
            template.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.department_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.program_type.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus =
            statusFilter === 'all' || (statusFilter === 'active' && template.is_active) || (statusFilter === 'inactive' && !template.is_active);

        const matchesDepartment = departmentFilter === 'all' || template.department_name === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // 獲取所有系所
    const departments = Array.from(new Set(templates.map((template) => template.department_name)));

    // 處理檔案選擇
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setData('template_file', file);

            // 如果未填寫模板名稱，使用檔案名稱（不含副檔名）
            if (!data.template_name) {
                const fileName = file.name.replace(/\.[^/.]+$/, '');
                setData('template_name', fileName);
            }
        }
    };

    // 處理表單提交
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        post('/admin/pdf-management/upload', {
            onSuccess: () => {
                reset();
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            },
        });
    };

    // 切換模板狀態
    const toggleTemplateStatus = (id: number) => {
        router.post(`/admin/pdf-management/toggle/${id}`);
    };

    // 刪除模板
    const deleteTemplate = (id: number) => {
        if (confirm('確定要刪除此模板嗎？此操作無法復原。')) {
            router.delete(`/admin/pdf-management/delete/${id}`);
        }
    };

    // 下載模板
    const downloadTemplate = (id: number) => {
        window.location.href = `/admin/pdf-management/download/${id}`;
    };

    // 更新提交方式設定
    const updateSubmissionSettings = (
        payload = {
            allow_pdf_upload: allowPdfUpload,
            allow_questionnaire_submission: allowQuestionnaireSubmission,
        },
    ) => {
        if (!payload.allow_pdf_upload && !payload.allow_questionnaire_submission) {
            alert('至少要允許一種提交方式');
            // 重新整理
            window.location.reload();
            return;
        }

        router.post('/admin/pdf-management/submission-settings', payload);
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="上傳方式管理" description="管理PDF模板和相關設定">
            <Head title="上傳管理" />

            <div className="space-y-6 p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="templates">問卷模板管理</TabsTrigger>
                        <TabsTrigger value="submission-settings">提交方式設定</TabsTrigger>
                        <TabsTrigger value="settings">上傳檔案設定</TabsTrigger>
                    </TabsList>

                    <TabsContent value="templates" className="space-y-6 pt-4">
                        {/* 統計資訊 */}
                        <StatsCards
                            stats={[
                                {
                                    title: '總模板數',
                                    value: templates.length,
                                    icon: FileText,
                                    color: 'default',
                                },
                                {
                                    title: '啟用模板',
                                    value: templates.filter((t) => t.is_active).length,
                                    icon: CheckCircle,
                                    color: 'green',
                                },
                                {
                                    title: '停用模板',
                                    value: templates.filter((t) => !t.is_active).length,
                                    icon: XCircle,
                                    color: 'red',
                                },
                                {
                                    title: '系所數量',
                                    value: departments.length,
                                    icon: HardDrive,
                                    color: 'blue',
                                },
                            ]}
                        />

                        {/* 搜尋和篩選 */}
                        <SearchAndFilter
                            searchTerm={searchTerm}
                            onSearchChange={setSearchTerm}
                            searchPlaceholder="搜尋模板名稱、系所或學程..."
                            filters={[
                                {
                                    key: 'status',
                                    label: '狀態',
                                    value: statusFilter,
                                    onChange: setStatusFilter,
                                    options: [
                                        { value: 'active', label: '啟用' },
                                        { value: 'inactive', label: '停用' },
                                    ],
                                },
                                {
                                    key: 'department',
                                    label: '系所',
                                    value: departmentFilter,
                                    onChange: setDepartmentFilter,
                                    options: departments.map((dept) => ({ value: dept, label: dept })),
                                },
                            ]}
                        />

                        {/* 模板列表 */}
                        <DataTable
                            data={filteredTemplates}
                            title="PDF模板列表"
                            description={`顯示 ${filteredTemplates.length} / ${templates.length} 個模板`}
                            emptyMessage="沒有找到符合條件的模板"
                            columns={[
                                {
                                    key: 'template_name',
                                    label: '模板名稱',
                                    sortable: true,
                                },
                                {
                                    key: 'department_name',
                                    label: '系所',
                                    sortable: true,
                                },
                                {
                                    key: 'program_type',
                                    label: '學程',
                                    sortable: true,
                                },
                                {
                                    key: 'is_active',
                                    label: '狀態',
                                    sortable: true,
                                    render: (value) => <StatusBadge status={value ? 'active' : 'inactive'} type="system" />,
                                },
                                {
                                    key: 'questions_count',
                                    label: '問題數',
                                    sortable: true,
                                },
                                {
                                    key: 'created_at',
                                    label: '建立時間',
                                    sortable: true,
                                },
                            ]}
                            actions={[
                                {
                                    key: 'toggle',
                                    label: '切換禁用狀態',
                                    icon: <Settings className="mr-1 h-4 w-4" />,
                                    variant: 'outline',
                                    onClick: (row) => toggleTemplateStatus(row.id),
                                },
                            ]}
                        />
                    </TabsContent>

                    <TabsContent value="submission-settings" className="space-y-6 pt-4">
                        {/* 提交方式設定 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="h-5 w-5" />
                                    推薦人提交方式設定
                                </CardTitle>
                                <CardDescription>控制推薦人可使用的提交方式</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between rounded-lg border p-4">
                                        <div>
                                            <p className="font-medium">允許上傳PDF檔案</p>
                                            <p className="text-sm text-gray-500">推薦人可以上傳自己準備的PDF推薦函</p>
                                        </div>
                                        <Switch
                                            checked={allowPdfUpload}
                                            onCheckedChange={(checked) => {
                                                const newPdfUpload = checked;
                                                const newQuestionnaire = allowQuestionnaireSubmission; // 保持問卷提交狀態不變
                                                setAllowPdfUpload(newPdfUpload);
                                                setAllowQuestionnaireSubmission(newQuestionnaire);

                                                updateSubmissionSettings({
                                                    allow_pdf_upload: newPdfUpload,
                                                    allow_questionnaire_submission: newQuestionnaire,
                                                });
                                            }}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between rounded-lg border p-4">
                                        <div>
                                            <p className="font-medium">允許線上問卷填寫</p>
                                            <p className="text-sm text-gray-500">推薦人可以填寫線上問卷，系統自動生成PDF</p>
                                        </div>
                                        <Switch
                                            checked={allowQuestionnaireSubmission}
                                            onCheckedChange={(checked) => {
                                                const newQuestionnaire = checked;
                                                const newPdfUpload = allowPdfUpload; // 保持PDF上傳狀態
                                                setAllowQuestionnaireSubmission(newQuestionnaire);
                                                setAllowPdfUpload(newPdfUpload);

                                                updateSubmissionSettings({
                                                    allow_pdf_upload: newPdfUpload,
                                                    allow_questionnaire_submission: newQuestionnaire,
                                                });
                                            }}
                                        />
                                    </div>

                                    <div className="rounded-lg border p-4">
                                        <h3 className="mb-2 font-medium">提交方式說明</h3>
                                        <div className="space-y-2 text-sm text-gray-600">
                                            <p>• 當兩種方式都啟用時，推薦人可以選擇其中一種方式提交</p>
                                            <p>• 如果只啟用一種方式，推薦人只能使用該方式提交</p>
                                            <p>• 建議同時啟用兩種方式，提供推薦人更多彈性</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="settings" className="space-y-6 pt-4">
                        {/* PDF設定 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="h-5 w-5" />
                                    PDF設定
                                </CardTitle>
                                <CardDescription>管理PDF相關設定</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    <div className="rounded-lg border p-4">
                                        <h3 className="mb-2 font-medium">上傳設定</h3>
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <p className="text-sm font-medium">最大檔案大小</p>
                                                <p className="text-sm text-gray-500">{pdf_settings.max_upload_size / 1024} MB</p>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium">允許的檔案類型</p>
                                                <p className="text-sm text-gray-500">{pdf_settings.allowed_types.join(', ')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="rounded-lg border p-4">
                                        <h3 className="mb-2 font-medium">存儲設定</h3>
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <p className="text-sm font-medium">存儲路徑</p>
                                                <p className="text-sm text-gray-500">{pdf_settings.storage_path}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* 警告訊息 */}
                        <Card className="border-orange-200 bg-orange-50">
                            <CardContent className="p-4">
                                <div className="flex items-start gap-3">
                                    <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium text-orange-800">注意事項</p>
                                        <p className="text-sm text-orange-700">
                                            修改PDF設定可能會影響系統中已存在的PDF檔案。建議在進行重要設定變更前先備份資料。
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
