<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\QuestionnaireTemplate;
use App\Models\SystemLog;
use App\Models\SystemSetting;
use App\Services\PdfService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

/**
 * PDF管理控制器
 * 
 * 管理PDFPDF上傳功能、上傳和相關設定
 */
class PdfManagementController extends Controller
{
    /**
     * 顯示PDF管理頁面
     */
    public function index()
    {
        // 獲取問卷PDF上傳功能（用於PDF管理）
        $templates = QuestionnaireTemplate::orderBy('department_name')
            ->orderBy('program_type')
            ->get()
            ->map(function ($template) {
                return [
                    'id' => $template->id,
                    'template_name' => $template->template_name,
                    'department_name' => $template->department_name,
                    'program_type' => $template->program_type,
                    'is_active' => $template->is_active,
                    'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => '系統',
                    'questions_count' => count($template->questions ?? [])
                ];
            });

        $breadcrumbs = [
            ['title' => '儀表板', 'href' => '/dashboard'],
            ['title' => 'PDF管理', 'href' => '/admin/pdf-management'],
        ];

        return Inertia::render('admin/pdf-management', [
            'templates' => $templates,
            'breadcrumbs' => $breadcrumbs,
            'pdf_settings' => [
                'max_upload_size' => config('recommendation.upload.max_size'),
                'allowed_types' => config('recommendation.upload.allowed_types'),
                'storage_path' => config('recommendation.pdf.storage.path'),
            ],
            'submission_settings' => [
                'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
                'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
            ]
        ]);
    }

    /**
     * 切換功能開放啟用狀態
     */
    public function toggleTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $template->is_active = !$template->is_active;
            $template->save();

            $user = Auth::user();
            $action = $template->is_active ? '啟用' : '停用';

            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} {$action}了PDF上傳功能：{$template->template_name}",
                [
                    'template_id' => $id,
                    'is_active' => $template->is_active
                ]
            );

            return back()->with('success', "PDF上傳功能已{$action}");
        } catch (\Exception $e) {
            Log::error('PDF上傳功能狀態切換失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'template' => '操作失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取PDF設定資訊
     */
    public function getSettings()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'upload' => config('recommendation.upload'),
                'pdf' => config('recommendation.pdf'),
                'storage_info' => [
                    'disk' => config('recommendation.pdf.storage.disk'),
                    'path' => config('recommendation.pdf.storage.path'),
                    'available_space' => $this->getAvailableStorageSpace()
                ]
            ]
        ]);
    }

    /**
     * 更新提交方式設定
     */
    public function updateSubmissionSettings(Request $request)
    {
        $request->validate([
            'allow_pdf_upload' => 'required|boolean',
            'allow_questionnaire_submission' => 'required|boolean',
        ]);

        try {
            // 至少要允許一種提交方式
            if (!$request->allow_pdf_upload && !$request->allow_questionnaire_submission) {
                return back()->withErrors([
                    'submission_settings' => '至少要允許一種提交方式'
                ]);
            }

            SystemSetting::setAllowPdfUpload($request->allow_pdf_upload);
            SystemSetting::setAllowQuestionnaireSubmission($request->allow_questionnaire_submission);

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} 更新了提交方式設定",
                [
                    'allow_pdf_upload' => $request->allow_pdf_upload,
                    'allow_questionnaire_submission' => $request->allow_questionnaire_submission,
                ]
            );

            return back()->with('success', '提交方式設定已更新');
        } catch (\Exception $e) {
            Log::error('提交方式設定更新失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            SystemLog::logError(
                SystemLog::ACTION_UPDATE,
                '提交方式設定更新失敗',
                $e
            );

            return back()->withErrors([
                'submission_settings' => '設定更新失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取可用存儲空間
     */
    private function getAvailableStorageSpace(): array
    {
        try {
            $path = storage_path('app');

            $totalSpace = disk_total_space($path);
            $freeSpace = disk_free_space($path);
            $usedSpace = $totalSpace - $freeSpace;

            return [
                'total' => $totalSpace,
                'free' => $freeSpace,
                'used' => $usedSpace,
                'usage_percentage' => $totalSpace > 0 ? round(($usedSpace / $totalSpace) * 100, 2) : 0
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'free' => 0,
                'used' => 0,
                'usage_percentage' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
